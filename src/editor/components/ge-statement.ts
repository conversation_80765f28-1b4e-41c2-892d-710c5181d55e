import { LitElement, html, css, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { consume } from '@lit/context';
import { Ref, createRef, ref } from 'lit/directives/ref.js';

import {
  AbstractStatementWithArgs,
  CompoundStatement,
  CompoundStatementWithArgs,
  Program,
  ProgramStatement,
  DeviceMetadata,
  initDefaultArgumentType,
  assignUuidToBlock,
} from '@vpl/program';
import { Argument, Language } from '@vpl/language';
import Types from '@vpl/types.ts';

import { languageContext, programContext } from '@/editor/context/editor-context';
import {
  editorVariablesModalCustomEvent,
  graphicalEditorCustomEvent,
  procedureEditorCustomEvent,
  statementCustomEvent,
  deviceMetadataCustomEvent,
  textEditorCustomEvent,
} from '@/editor/editor-custom-events';

import { globalStyles } from '../global-styles';
import * as icons from '../icons';
import { EditorModal } from './editor-modal';

@customElement('ge-statement')
export class GEStatement extends LitElement {
  //#region Styles
  static styles = [
    globalStyles,
    css`
      :host {
        display: flex;
        flex-direction: column;
      }

      .highlight-active {
        box-shadow: 0 0 10px 2px rgba(255, 255, 0, 0.8);
        border: 2px solid rgba(255, 255, 0, 0.8);
      }

      .highlight-active .nested {
        box-shadow: 0 0 10px 2px rgba(255, 255, 0, 0.8);
        border: 2px solid rgba(255, 255, 0, 0.8);
      }

      .expr-arg {
        white-space: nowrap;
        overflow-x: auto;
        width: 100%;
      }

      .statement-controls {
        display: flex;
        gap: 0.35rem;
        margin-left: auto;
        height: 100%;
      }

      .device-count {
        display: flex;
        align-items: center;
        margin-right: 0.5rem;
        font-weight: bold;
      }

      .device-count-incomplete {
        color: var(--red-600);
      }

      .device-count-complete {
        color: var(--green-600);
      }

      .statement-label-wrapper {
        display: flex;
        align-items: center;
        gap: 0.35rem;
        position: relative;
        /* width: 100%; */
      }

      .statement-header {
        display: flex;
        flex-direction: row;
        /* justify-content: space-between; */
        align-items: center;
        gap: 0.35rem;
        padding: 0.5rem;
        border-top-left-radius: 0.5rem;
        border-top-right-radius: 0.5rem;
        height: 55px;
      }

      .nested {
        padding: 0.75rem;
        border-bottom-left-radius: 0.5rem;
        border-bottom-right-radius: 0.5rem;
      }

      .hidden {
        display: none;
      }

      .bottom-radius {
        border-bottom-left-radius: 0.5rem;
        border-bottom-right-radius: 0.5rem;
      }

      .statement-controls-modal-wrapper {
        display: flex;
        position: relative;
      }

      .statement-controls-modal {
        bottom: 0;
        left: -94px;
      }

      .statement-controls-modal::part(dialog) {
        padding: 0;
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
      }

      .statement-controls-buttons {
        display: flex;
        flex-direction: column;
      }

      .statement-controls-buttons editor-button {
        display: flex;
        justify-items: center;
        gap: 0.25rem;
        white-space: nowrap;
        box-shadow: none;
        border: none;
        border-bottom: 1px solid var(--gray-300);
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }

      .statement-controls-buttons editor-button:last-child {
        border-bottom-left-radius: 0.5rem;
        border-bottom-right-radius: 0.5rem;
        border-bottom: none;
      }

      .remove-statement-button {
        color: var(--red-600);
      }

      .statement-controls-expand-button {
      }

      .expand-nested-block-button {
        display: flex;
        align-items: center;
        padding-left: 4px;
        padding-right: 4px;
        cursor: pointer;
      }

      .statement-arguments-wrapper {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }

      .user-proc-wrapper {
        display: block;
        padding: 0;
        border: none;
        box-shadow: none;
        background: none;
      }

      .ok-button {
        display: flex;
        justify-content: center;
        color: var(--green-600);
        gap: 4px;
      }

      .stmt-brief-desc-wrapper {
        max-width: 800px;
        text-align: justify;
        text-justify: inter-word;
      }

      .stmt-desc-modal {
        position: absolute;
        bottom: -4px;
      }

      .stmt-icon {
        cursor: help;
      }

      .stmt-desc-modal * {
        outline: none;
      }

      .stmt-desc-inner-wrapper {
        display: flex;
        flex-direction: column;
        gap: 8px;
        max-height: 500px;
        overflow: auto;
      }

      .stmt-desc-item-wrapper {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .stmt-desc-label {
        font-weight: 600;
        font-size: 1.125rem;
      }

      .divider {
        width: 100%;
        height: 1px;
        background: var(--gray-300);
        margin-bottom: 8px;
      }

      .modal-info-header {
        background-color: var(--gray-100);
        color: var(--gray-700);
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        border-radius: 0.25rem;
        font-weight: bold;
        text-align: center;
        border: 1px solid var(--gray-300);
      }
        @media (min-width: 500px) {
        .statement-label {
          white-space: nowrap;
        }
      }
    `,
  ];
  //#endregion

  //#region Props
  @property() statement: ProgramStatement;
  @property() index: number;
  @property() deviceIndex: number = -1;
  @property() nestedBlockVisible: boolean = true;
  @property() isProcBody: boolean = false;
  @property() isExample: boolean = false;
  @property() exampleBlockIsVisible: boolean = false;
  @property({ type: Boolean }) skeletonizeMode: boolean = false;
  @property({ type: Boolean }) restrainedMode: boolean = false;
  @property({ type: Boolean }) isHighlighted: boolean = false;
  @property({ type: Boolean }) isSelected: boolean = false;
  @property({ type: Boolean }) isNestedUserProcedure: boolean = false;

  @property({ type: Object }) procedureBlockCopy: any = [];
  @property() uuidMetadata: string;
  @property() editorMode: 'edit' | 'initialize' = 'edit';
  @property() initializedDeviceCount: number = 0;
  @property() totalDeviceCount: number = 0;
  @property({ type: Object }) procedureDeviceIndices: Map<string, number> = new Map();
  //#endregion

  //#region Context
  @consume({ context: languageContext })
  @property()
  language?: Language;

  @consume({ context: programContext })
  @property()
  program?: Program;
  //#endregion

  //#region Refs
  statementHeaderRef: Ref<HTMLElement> = createRef();
  statementNestedBlockRef: Ref<HTMLElement> = createRef();
  statementControlsModalRef: Ref<EditorModal> = createRef();
  multipleArgsModalRef: Ref<EditorModal> = createRef();
  procModalRef: Ref<EditorModal> = createRef();
  stmtDescModalRef: Ref<EditorModal> = createRef();
  //#endregion

  //#region Lifecycle
  constructor() {
    super();
    setTimeout(() => this.updateDeviceCounts(), 0);

    this.addEventListener(editorVariablesModalCustomEvent.VARIABLE_SELECTED, (_e: CustomEvent) => {
      if (this.statement.id === 'setvar') {
        (this.statement as AbstractStatementWithArgs | CompoundStatementWithArgs).arguments[1].type = this.program.header
          .userVariables[
          (this.statement as AbstractStatementWithArgs | CompoundStatementWithArgs).arguments[0].value as string
        ]
          ? this.program.header.userVariables[
              (this.statement as AbstractStatementWithArgs | CompoundStatementWithArgs).arguments[0].value as string
            ].type
          : this.language.variables[
              (this.statement as AbstractStatementWithArgs | CompoundStatementWithArgs).arguments[0].value as string
            ]
          ? 'device'
          : 'invalid';
        (this.statement as AbstractStatementWithArgs | CompoundStatementWithArgs).arguments[1].value =
          (this.statement as AbstractStatementWithArgs | CompoundStatementWithArgs).arguments[1].type === Types.boolean_expression
            ? initDefaultArgumentType(Types.boolean_expression)
            : null;

        const event = new CustomEvent(graphicalEditorCustomEvent.PROGRAM_UPDATED, {
          bubbles: true,
          composed: true,
        });
        this.dispatchEvent(event);
      }
    });

    this.addEventListener('device-selection-changed', (e: CustomEvent) => {
      if (this.statement._uuid && this.statement._uuid === e.detail.deviceUuid) {
        this.requestUpdate();
      }

      if (this.language?.statements[this.statement.id]?.isUserProcedure &&
          this.statement._uuid === e.detail.procedureUuid) {
        this.updateDeviceCounts();
        this.requestUpdate();
      }
    });

    this.addEventListener(deviceMetadataCustomEvent.VALUE_CHANGED, (_e: CustomEvent) => {
      const isDeviceStatement = !!(this.statement as any).id && !(this.statement as any).devices &&
                                ((this.statement as any).id === 'deviceType' ||
                                (this.language?.deviceList?.includes((this.statement as any).id.split('.')[0])));

      if (this.statement._uuid && this.editorMode === 'initialize' && isDeviceStatement && this.uuidMetadata) {
        const findProcedureEntry = (block: any[], targetUuid: string): any => {
          const directEntry = block.find(stmt => stmt._uuid === targetUuid);
          if (directEntry) return directEntry;

          for (const stmt of block) {
            if (stmt.block && Array.isArray(stmt.block)) {
              const nestedEntry = findProcedureEntry(stmt.block, targetUuid);
              if (nestedEntry) return nestedEntry;
            }
          }
          return null;
        };

        const initProcEntry = findProcedureEntry(this.program.block, this.uuidMetadata);

        if (initProcEntry) {
          this.updateDeviceMetadataValue();
        } else {
          this.updateDeviceMetadataValue();
        }
      }
      this.requestUpdate();
    });

    this.addEventListener(procedureEditorCustomEvent.PROCEDURE_MODAL_CLOSED, (_e: CustomEvent) => {
      this.restrainedMode = false;
      this.editorMode = 'edit';
      this.requestUpdate();
    });

    this.addEventListener(deviceMetadataCustomEvent.REOPEN_PROCEDURE_MODAL, (e: CustomEvent) => {
      if (this.language?.statements[this.statement.id]?.isUserProcedure &&
          this.statement._uuid === e.detail.procedureUuid) {
        if (this.procModalRef.value) {
          this.procModalRef.value.hideModal();
        }
        this.handleShowProcDef();
      }
    });

    this.addEventListener(graphicalEditorCustomEvent.PROGRAM_UPDATED, () => {
      if (this.language?.statements[this.statement?.id]?.isUserProcedure && !this.isProcBody) {
        this.updateDeviceCounts();
        this.requestUpdate();
      }
    });

    this.addEventListener('update-device-counts', (e: CustomEvent) => {
      if (this.language?.statements[this.statement?.id]?.isUserProcedure && !this.isProcBody) {
        const procedureUuid = e.detail?.procedureUuid;
        if (!procedureUuid || this.statement._uuid === procedureUuid) {
          this.updateDeviceCounts();
          this.requestUpdate();
        }
      }
    });
  }

  //#region Methods
  updateDeviceMetadataValue() {
    if (!this.statement._uuid) {
      return;
    }

    const findProcedureEntry = (block: any[], targetUuid: string): any => {
      const directEntry = block.find(stmt => stmt._uuid === targetUuid);
      if (directEntry) return directEntry;

      for (const stmt of block) {
        if (stmt.block && Array.isArray(stmt.block)) {
          const nestedEntry = findProcedureEntry(stmt.block, targetUuid);
          if (nestedEntry) return nestedEntry;
        }
      }
      return null;
    };

    const procInitEntry = findProcedureEntry(this.program.block, this.uuidMetadata);
    if (!procInitEntry) {
      return;
    }

    if (this.deviceIndex === -1) {
      return;
    }

    let deviceTypeIndex = this.deviceIndex;
    if (deviceTypeIndex === -1) {
      return;
    }

    const deviceEntry = procInitEntry.devices[deviceTypeIndex];
    if (deviceEntry && (this.statement as AbstractStatementWithArgs).arguments) {
      const argValue = (this.statement as AbstractStatementWithArgs).arguments[0]?.value;

      if (argValue !== undefined && argValue !== null) {
        if (deviceEntry.arguments && deviceEntry.arguments[0]) {
          deviceEntry.arguments[0].value = argValue;
        }
      }

      if (!deviceEntry.id || deviceEntry.id === '') {
        deviceEntry.id = this.statement.id;
      }
    }
  }

  countDeviceTypeBlocks(block: any[]): number {
    let count = 0;

    const countDevicesInBlock = (blockToCount: any[]) => {
      if (!blockToCount || !Array.isArray(blockToCount)) return;

      for (const stmt of blockToCount) {
        if (stmt.id === 'deviceType') {
          count++;
        } else if (stmt.id && this.language?.deviceList) {
          const deviceName = stmt.id.split('.')[0];
          if (this.language.deviceList.includes(deviceName)) {
            count++;
          }
        }
        if (stmt.block && Array.isArray(stmt.block)) {
          countDevicesInBlock(stmt.block);
        }
      }
    };

    countDevicesInBlock(block);
    return count;
  }

  getDeviceTypeIndices(): number[] {
    if (!this.language?.statements || !this.statement?.id) return [];

    const procedureBlock = this.program.header.userProcedures[this.statement.id];
    if (!procedureBlock) return [];

    const indices: number[] = [];
    let currentIndex = 0;

    const collectDeviceIndices = (blockToScan: any[]) => {
      if (!blockToScan || !Array.isArray(blockToScan)) return;

      for (const stmt of blockToScan) {
        if (stmt.id === 'deviceType') {
          indices.push(currentIndex);
          currentIndex++;
        } else if (stmt.id && this.language?.deviceList) {
          const deviceName = stmt.id.split('.')[0];
          if (this.language.deviceList.includes(deviceName)) {
            indices.push(currentIndex);
            currentIndex++;
          }
        }
        if (stmt.block && Array.isArray(stmt.block)) {
          collectDeviceIndices(stmt.block);
        }
      }
    };

    collectDeviceIndices(procedureBlock);
    return indices;
  }

  countInitializedDevices(procedureUuid: string): number {
    if (!this.program || !procedureUuid) return 0;

    const findProcedureEntry = (block: any[], targetUuid: string): any => {
      const directEntry = block.find(stmt => stmt._uuid === targetUuid);
      if (directEntry) return directEntry;

      for (const stmt of block) {
        if (stmt.block && Array.isArray(stmt.block)) {
          const nestedEntry = findProcedureEntry(stmt.block, targetUuid);
          if (nestedEntry) return nestedEntry;
        }
      }
      return null;
    };

    const procedureEntry = findProcedureEntry(this.program.block, procedureUuid);

    if (!procedureEntry || !procedureEntry.devices) return 0;

    const initializedCount = procedureEntry.devices.filter((device: DeviceMetadata) => {
      if (device.id === 'deviceType') {
        return false;
      }

      if (this.language.statements[device.id]) {
        return true;
      }

      return false;
    }).length;

    return initializedCount;
  }

  areAllDevicesInitialized(): boolean {
    return this.initializedDeviceCount === this.totalDeviceCount && this.totalDeviceCount > 0;
  }

  updateDeviceCounts() {
    if (!this.language?.statements || !this.statement?.id) return;

    if (this.language.statements[this.statement.id]?.isUserProcedure && !this.isProcBody) {
      const procedureBlock = this.program.header.userProcedures[this.statement.id];
      if (procedureBlock) {
        this.totalDeviceCount = this.countDeviceTypeBlocks(procedureBlock);
        this.initializedDeviceCount = this.countInitializedDevices(this.statement._uuid);

        // Match the number of devices array entries to deviceType statements
        this.matchNumberOfDevices();
      }
    }
  }

  matchNumberOfDevices() {
    if (!this.program || !this.statement?._uuid) return;

    // Find the procedure entry recursively through the program structure
    const findProcedureEntry = (block: any[], targetUuid: string): any => {
      // First check if the entry is in this block
      const directEntry = block.find(stmt => stmt._uuid === targetUuid);
      if (directEntry) return directEntry;

      // If not found directly, search in nested blocks
      for (const stmt of block) {
        if (stmt.block && Array.isArray(stmt.block)) {
          const nestedEntry = findProcedureEntry(stmt.block, targetUuid);
          if (nestedEntry) return nestedEntry;
        }
      }

      return null;
    };

    const procedureEntry = findProcedureEntry(this.program.block, this.statement._uuid);

    if (!procedureEntry) return;

    // Ensure devices array exists
    if (!procedureEntry.devices) {
      procedureEntry.devices = [];
    }

    // Get the current device indices from the procedure block
    const deviceIndices = this.getDeviceTypeIndices();
    const currentDevicesLength = procedureEntry.devices.length;
    const targetDeviceCount = deviceIndices.length;

    let needsTextEditorUpdate = false;

    if (currentDevicesLength !== targetDeviceCount) {
      // Create a new devices array that matches the current deviceType structure
      const newDevicesArray = [];

      for (let i = 0; i < targetDeviceCount; i++) {
        if (i < currentDevicesLength && procedureEntry.devices[i]) {
          // Keep existing device entry if it exists at this index
          newDevicesArray.push(procedureEntry.devices[i]);
        } else {
          // Add empty device entry for new deviceType
          newDevicesArray.push({
            id: 'deviceType',
            arguments: []
          });
        }
      }

      procedureEntry.devices = newDevicesArray;
      needsTextEditorUpdate = true;
    }

    // Update the text editor if changes were made
    if (needsTextEditorUpdate) {
      this.updateTextEditor();
    }
  }

  updateTextEditor() {
    // Dispatch events to update both graphical and text editors
    const graphicalEditorEvent = new CustomEvent(graphicalEditorCustomEvent.PROGRAM_UPDATED, {
      bubbles: true,
      composed: true,
      detail: { programBodyUpdated: true }
    });
    this.dispatchEvent(graphicalEditorEvent);

    const textEditorEvent = new CustomEvent(textEditorCustomEvent.PROGRAM_UPDATED, {
      bubbles: true,
      composed: true
    });
    this.dispatchEvent(textEditorEvent);
  }

  updated(changedProperties: Map<string, any>) {
    if (this.statement._uuid && this.program) {
      if (changedProperties.has('skeletonizeMode') || changedProperties.has('statement')) {
        this.isHighlighted = this.skeletonizeMode && this.program.header.skeletonize_uuid.includes(this.statement._uuid);
      }
    }

    this.updateDeviceCounts();
    if (changedProperties.has('statement') || changedProperties.has('statement.id')) {
      this.updateDeviceCounts();
    }

    if (this.statementHeaderRef.value) {
      this.statementHeaderRef.value.setAttribute(
        'style',
        `background-color: ${
          this.language.statements[this.statement.isInvalid ? '_err' : this.statement.id].backgroundColor
        }; color: ${this.language.statements[this.statement.isInvalid ? '_err' : this.statement.id].foregroundColor}; ${
          this.statement.isInvalid ? 'border: 4px dashed #facc15' : ''
        }`
      );
    }

    if (this.statementNestedBlockRef.value) {
      const bgColor = this.language.statements[this.statement.isInvalid ? '_err' : this.statement.id].backgroundColor;
      const fgColor = this.language.statements[this.statement.isInvalid ? '_err' : this.statement.id].foregroundColor;
      const isUserProcedure = this.language.statements[this.statement.id]?.isUserProcedure;
      const transparency = (this.skeletonizeMode && isUserProcedure) ? '' : '3a';

      if (this.statementNestedBlockRef.value) {
        this.statementNestedBlockRef.value.setAttribute(
          'style',
          `background-color: ${bgColor}${transparency}; color: ${fgColor};`
        );
      }
    }
  }
  //#endregion

  //#region Handlers
  handleToggleNestedBlockVisibility() {
    this.nestedBlockVisible = !this.nestedBlockVisible;
  }

  handleRemoveStatement(e: Event) {
    const event = new CustomEvent(statementCustomEvent.REMOVE, {
      bubbles: false,
      composed: true,
      detail: { index: this.index },
    });
    this.dispatchEvent(event);
    this.handleHideStatementControlsModal();
    e.stopPropagation();
  }

  handleMoveStatementUp(e: Event) {
    const event = new CustomEvent(statementCustomEvent.MOVE_UP, {
      bubbles: false,
      composed: true,
      detail: { index: this.index },
    });
    this.dispatchEvent(event);
    this.handleHideStatementControlsModal();
    e.stopPropagation();
  }

  handleMoveStatementDown(e: Event) {
    const event = new CustomEvent(statementCustomEvent.MOVE_DOWN, {
      bubbles: false,
      composed: true,
      detail: { index: this.index },
    });
    this.dispatchEvent(event);
    this.handleHideStatementControlsModal();
    e.stopPropagation();
  }

  handleToggleStatementControlsModal(e: Event) {
    if (this.statementControlsModalRef.value) {
      this.statementControlsModalRef.value.toggleModal();
    }
    e.stopPropagation();
  }

  handleHideStatementControlsModal() {
    if (this.statementControlsModalRef.value) {
      this.statementControlsModalRef.value.hideModal();
    }
  }

  handleShowProcDef() {
    if (this.skeletonizeMode) return;
    this.editorMode = 'initialize';
    this.restrainedMode = true;

    const originalProcedureBlock = this.program.header.userProcedures[this.statement.id];
    if (originalProcedureBlock) {
      this.procedureBlockCopy = JSON.parse(JSON.stringify(originalProcedureBlock));

      assignUuidToBlock(this.procedureBlockCopy);
      this.uuidMetadata = this.statement._uuid;

      const findProcedureEntry = (block: any[], targetUuid: string): any => {
        const directEntry = block.find(stmt => stmt._uuid === targetUuid);
        if (directEntry) return directEntry;

        for (const stmt of block) {
          if (stmt.block && Array.isArray(stmt.block)) {
            const nestedEntry = findProcedureEntry(stmt.block, targetUuid);
            if (nestedEntry) return nestedEntry;
          }
        }
        return null;
      };

      const procedureEntry = findProcedureEntry(this.program.block, this.statement._uuid);
      const sharedState = { deviceTypeIndex: 0 };
      const parseBlock = (block: any[]) => {
        block.forEach((stmt: any, index: number) => {
          if (stmt.id === 'deviceType') {
            const deviceEntry = procedureEntry && procedureEntry.devices
            ? procedureEntry.devices[sharedState.deviceTypeIndex]: null;
            let deviceID = deviceEntry?.id || 'deviceType';

            if (deviceEntry) {
              const deviceIDName = deviceID.split('.')[0];
              if (!this.language.deviceList.includes(deviceIDName)) deviceID = 'deviceType';
            } else {
              deviceID = 'deviceType';
            }

            if (deviceID === 'deviceType') {
              const deviceTypeValue = stmt.arguments && stmt.arguments[0] ? stmt.arguments[0].value : '';
              block[index] = {
                ... this.language.statements[deviceID],
                id: deviceID,
                _uuid: stmt._uuid,
                arguments: [
                  {
                    type: Types.string,
                    value: deviceTypeValue,
                  },
                ],
                isInvalid: false,
              };
            } else {
              const langStatement = this.language.statements[deviceID];
              const newArguments = [];

              if (langStatement && (langStatement as any).arguments) {
                const argDefs = (langStatement as any).arguments;

                argDefs.forEach((argDef: any, argIndex: number) => {
                  const newArg: any = {
                    type: argDef.type,
                    isInvalid: false
                  };

                  if (deviceEntry && deviceEntry.arguments && deviceEntry.arguments[argIndex] !== undefined) {
                    newArg.value = deviceEntry.arguments[argIndex].value;
                  } else {
                    if (argDef.type === 'str_opt' || argDef.type === 'num_opt') {
                      newArg.value = argDef.options[0].id;
                    } else {
                      newArg.value = initDefaultArgumentType(argDef.type);
                    }
                  }
                  newArguments.push(newArg);
                });
              }

              block[index] = {
                ... this.language.statements[deviceID],
                id: deviceID,
                _uuid: stmt._uuid,
                arguments: newArguments,
                isInvalid: false,
              };
            }
            sharedState.deviceTypeIndex++;
          }

          if (stmt.block && Array.isArray(stmt.block)) {
            parseBlock(stmt.block);
          }
        });
      };

      parseBlock(this.procedureBlockCopy);
      this.requestUpdate();
    }

    if (this.procModalRef.value) {
      this.procModalRef.value.showModal();
    }
  }

  handleShowStmtDescModal(e: Event) {
    if (!this.exampleBlockIsVisible) {
      this.exampleBlockIsVisible = true;
    }
    if (this.stmtDescModalRef.value) {
      this.stmtDescModalRef.value.showModal();
    }
    e.stopPropagation();
  }
  //#endregion

  //#region Templates
  multipleArgumentTemplate(argumentsArray: Argument[]) {
    return html`
      <editor-button class="expr-arg" @click="${() => this.multipleArgsModalRef.value?.showModal()}">
        <div style="display: flex; gap: 4px; align-items: center; flex-direction: row;">
          <editor-icon .icon="${icons.threeDots}"></editor-icon>
          <div style="white-space: nowrap;">Arguments</div>
        </div>
      </editor-button>
      <editor-modal ${ref(this.multipleArgsModalRef)} .modalTitle="${'Set Arguments'}">
        <div class="statement-arguments-wrapper">
          ${argumentsArray.map(
            (arg, i) =>
              html`
                <ge-statement-argument
                  .argument="${arg}"
                  .argPosition="${i}"
                  .stmtId="${this.statement.id}"
                  .showLabel="${true}"
                  .variableKey="${this.statement.id === 'setvar' && arg.type === Types.unknown
                    ? (this.statement as AbstractStatementWithArgs | CompoundStatementWithArgs).arguments[0].value
                    : null}">
                </ge-statement-argument>
              `
          )}
          <editor-button class="ok-button" @click="${() => this.multipleArgsModalRef.value?.hideModal()}">
            <editor-icon .icon="${icons.checkLg}"></editor-icon>
            <span>OK</span>
          </editor-button>
        </div>
      </editor-modal>
    `;
  }

  statementTemplate(hasNestedBlock: boolean) {
    if (!this.statement || !this.statement.id || !this.language || !this.language.statements) {
      return html`<div class="error-statement">Error: Invalid statement data</div>`;
    }

    // Check if the statement exists in the language
    if (!this.language.statements[this.statement.id]) {
      return html`<div class="error-statement">Error: Unknown statement type: ${this.statement.id}</div>`;
    }

    const statementDef = this.language.statements[this.statement.isInvalid ? '_err' : this.statement.id];
    const isUserProcedure = statementDef.isUserProcedure || false;

    return html`
      <div
        ${ref(this.statementHeaderRef)}
        class="statement-header ${!hasNestedBlock || !this.nestedBlockVisible ? 'bottom-radius' : ''} ${isUserProcedure
          ? 'user-proc'
          : ''}">
        <div class="statement-label-wrapper">
          ${statementDef.icon
            ? html`
                <editor-icon
                  class="stmt-icon"
                  title="Show Help"
                  @click="${this.handleShowStmtDescModal}"
                  .icon="${icons[statementDef.icon] || icons.questionCircle}"
                  .color="${statementDef.foregroundColor}"
                  .width="${24}"
                  .height="${24}">
                </editor-icon>
                ${this.language.statements[this.statement.id].description
                  ? html`
                      <editor-modal
                        class="stmt-desc-modal"
                        .modalTitle="${`Help for "${this.language.statements[this.statement.id].label}" statement`}"
                        .modalIcon="${icons.questionCircle}"
                        ${ref(this.stmtDescModalRef)}>
                        <div class="stmt-desc-inner-wrapper">
                          <div class="stmt-desc-item-wrapper">
                            <div class="stmt-desc-label">Description</div>
                            <div class="stmt-brief-desc-wrapper">
                              ${this.language.statements[this.statement.id].description.brief}
                            </div>
                          </div>
                          ${this.language.statements[this.statement.id].description.example
                            ? html`
                                <div class="stmt-desc-item-wrapper">
                                  <div class="stmt-desc-label">Example</div>
                                  <div class="stmt-brief-desc-wrapper">
                                    ${this.language.statements[this.statement.id].description.example.description}
                                  </div>
                                </div>
                                <div>
                                  ${this.exampleBlockIsVisible
                                    ? html`
                                        <div class="divider"></div>
                                        <ge-block
                                          .isExample="${true}"
                                          .block="${this.language.statements[this.statement.id].description.example
                                            .block}">
                                        </ge-block>
                                      `
                                    : nothing}
                                </div>
                              `
                            : nothing}
                        </div>
                      </editor-modal>
                    `
                  : nothing}
              `
            : nothing}

          <div class="statement-label">${statementDef.label || 'Unknown Statement'}</div>
        </div>
        ${(() => {
          // Check if statement has arguments
          const stmt = this.statement as AbstractStatementWithArgs | CompoundStatementWithArgs;
          if (!stmt.arguments) {
            return nothing;
          }

          // Check if arguments is an array
          if (!Array.isArray(stmt.arguments)) {
            return nothing;
          }

          // Handle single argument
          if (stmt.arguments.length === 1) {
            return html`
              <ge-statement-argument
                ?disabled="${this.statement.isInvalid || this.skeletonizeMode}"
                .argument="${stmt.arguments[0]}"
                .argPosition="${0}"
                .stmtId="${this.statement.id}"
                .isExample="${this.isExample}">
              </ge-statement-argument>
            `;
          }

          // Handle multiple arguments
          return this.multipleArgumentTemplate(stmt.arguments);
        })()}
        <div class="statement-controls">
          ${this.language.statements[this.statement.id]?.isUserProcedure && !this.isProcBody
            ? html`
              <div class="device-count ${this.areAllDevicesInitialized() ? 'device-count-complete' : 'device-count-incomplete'}"
                  title="Initialized devices / Total device blocks">
                ${this.initializedDeviceCount}/${this.totalDeviceCount}
              </div>
            `
            : nothing
          }
          <div class="statement-controls-modal-wrapper">
            ${!this.isExample && !this.skeletonizeMode && !(this.isProcBody && this.editorMode === 'initialize' && this.restrainedMode)
              ? html`
                  <editor-button
                    @click="${this.handleToggleStatementControlsModal}"
                    title="Statement Controls"
                    class="statement-controls-expand-button">
                    <editor-icon .icon="${icons.list}"></editor-icon>
                  </editor-button>
                  <editor-modal
                    class="statement-controls-modal"
                    .displayType="${'dialog'}"
                    .titleIsVisible="${false}"
                    .closeButtonIsVisible="${false}"
                    ${ref(this.statementControlsModalRef)}>
                    <div class="statement-controls-buttons">
                      <editor-button @click="${this.handleMoveStatementUp}" title="Move statement up">
                        <editor-icon .icon="${icons.arrowUp}"></editor-icon>
                        Move Up
                      </editor-button>
                      <editor-button @click="${this.handleMoveStatementDown}" title="Move statement down">
                        <editor-icon .icon="${icons.arrowDown}"></editor-icon>
                        Move Down
                      </editor-button>
                      <editor-button
                        @click="${this.handleRemoveStatement}"
                        title="Remove Statement"
                        class="remove-statement-button">
                        <editor-icon .icon="${icons.trash}"></editor-icon>
                        Delete
                      </editor-button>
                    </div>
                  </editor-modal>
                `
              : nothing}
          </div>
          ${(this.statement as CompoundStatement).block && !this.skeletonizeMode && !(this.isProcBody && this.editorMode === 'initialize' && this.restrainedMode)
            ? html`
                <div @click="${this.handleToggleNestedBlockVisibility}" class="expand-nested-block-button">
                  <editor-icon
                    .icon="${this.nestedBlockVisible ? icons.chevronDown : icons.chevronRight}"
                    .width="${18}"
                    .height="${18}"
                    title="Show Block"></editor-icon>
                </div>
              `
            : nothing}
        </div>
      </div>
    `;
  }
  //#endregion

  //#region Render
  render() {
    if (!this.statement || !this.statement.id || !this.language || !this.language.statements) {
      return html`<div class="error-statement">Error: Invalid statement data</div>`;
    }

    if (!this.language.statements[this.statement.id]) {
      return html`<div class="error-statement">Error: Unknown statement type: ${this.statement.id}</div>`;
    }

    const statementDef = this.language.statements[this.statement.isInvalid ? '_err' : this.statement.id];
    const isCompoundStatement = !!(this.statement as CompoundStatement).block;
    const isUserProcedure = statementDef.isUserProcedure && !this.isProcBody;

    return html`
      <div
        class="statement-wrapper ${this.isHighlighted || this.isSelected ? 'highlight-active' : ''}"
        uuid="${this.statement._uuid || ''}"
        @click="${() => {
          if (this.statement.isInvalid) {
            return;
          }

          const event = new CustomEvent('toggle-statement-selection', {
            bubbles: true,
            composed: true,
            detail: { uuid: this.statement._uuid },
          });
          this.dispatchEvent(event);
        }}">
        ${isCompoundStatement
          ? html`
              ${this.statementTemplate(true)}
              <ge-block
                ${ref(this.statementNestedBlockRef)}
                style="background-color: ${statementDef.backgroundColor || '#cccccc'}aa;"
                class="nested ${this.nestedBlockVisible ? '' : 'hidden'} ${this.isHighlighted ? 'highlight-active' : ''}"
                .block="${(this.statement as CompoundStatement).block}"
                .parentStmt="${this.statement}"
                .isProcBody="${this.isProcBody}"
                .skeletonizeMode="${this.skeletonizeMode}"
                .restrainedMode="${this.restrainedMode}"
                .tmpUUID="${this.uuidMetadata}"
                .editorMode="${this.editorMode}"
                .procedureDeviceIndices="${this.procedureDeviceIndices}"
                @click="${(e: Event) => {
                  e.stopPropagation();
                  const event = new CustomEvent('nested-click', {
                    bubbles: true,
                    composed: true,
                    detail: { uuid: this.statement._uuid },
                  });
                  this.dispatchEvent(event);
                }}">
              </ge-block>
            `
          : isUserProcedure
          ? html`
              <editor-button
                @click="${this.handleShowProcDef}"
                class="user-proc-wrapper"
                ?disabled="${this.skeletonizeMode}">
                ${this.statementTemplate(false)}
              </editor-button>
              <editor-modal
                ${ref(this.procModalRef)}
                .modalTitle="${statementDef.label || 'User Procedure'}"
                .modalIcon="${icons[statementDef.icon || 'lightningChargeFill']}"
                .backgroundColor="${statementDef.backgroundColor || '#d946ef'}"
                .foregroundColor="${statementDef.foregroundColor || '#ffffff'}"
                .isFullWidth="${true}"
                .isFullHeight="${true}"
                .isFromBody="${true}">
                <div class="modal-info-header">${this.editorMode === 'initialize' ? 'Initialize Procedure Parameters' : 'Edit Procedure Body'}</div>
                <ge-block
                  .isProcBody="${true}"
                  .isExample="${this.isExample}"
                  .block="${this.procedureBlockCopy}"
                  .skeletonizeMode="${this.skeletonizeMode}"
                  .restrainedMode="${this.restrainedMode}"
                  .tmpUUID="${this.uuidMetadata}"
                  .editorMode="${this.editorMode}"
                  .parentProcedureUuid="${this.statement._uuid}">
                </ge-block>
              </editor-modal>
            `
          : this.statementTemplate(false)}
      </div>
    `;
  }
  //#endregion
}

declare global {
  interface HTMLElementTagNameMap {
    'ge-statement': GEStatement;
  }
}
